services:
  uni-api:
    container_name: uni-api
    image: yym68686/uni-api:latest
    ports:
      - 8000:8000
    volumes:
      - ${API_YAML_PATH}:/home/<USER>
    restart: unless-stopped

  mybot:
    container_name: mybot
    image: yym68686/chatgpt:latest
    depends_on:
      - uni-api
    environment:
      - BOT_TOKEN=${BOT_TOKEN}
      - API_KEY=
      - BASE_URL=http://uni-api:8000/v1/chat/completions
      - GET_MODELS=True
    volumes:
      - ./user_configs:/home/<USER>
    ports:
      - 8080:8080